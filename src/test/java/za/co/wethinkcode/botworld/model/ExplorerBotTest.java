package za.co.wethinkcode.botworld.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static za.co.wethinkcode.botworld.model.Heading.*;

public class ExplorerBotTest
{
    private World world = new World( 3, 3 );

    @Test
    void newBot_hasDefaultHeadingNorth(){
        ExplorerBot bot = new ExplorerBot(world);
        assertEquals(North, bot.heading());
    }

    @Test
    void turnTo_changesHeading(){
        ExplorerBot bot = new ExplorerBot(world);
        bot.turnTo(South);
        assertEquals(South, bot.heading());

        bot.turnTo(East);
        assertEquals(East, bot.heading());

        bot.turnTo(West);
        assertEquals(West, bot.heading());

        bot.turnTo(North);
        assertEquals(North, bot.heading());
    }

    @Test
    void move_north_toANewPositionInsideTheWorld(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 1, 1); // Start at position (1,1)
        bot.turnTo(North);

        bot.move();

        assertEquals(new Coord(1, 0), bot.position()); // Should move north to (1,0)
    }

    @Test
    void move_south_toANewPositionInsideTheWorld(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 1, 1); // Start at position (1,1)
        bot.turnTo(South);

        bot.move();

        assertEquals(new Coord(1, 2), bot.position()); // Should move south to (1,2)
    }

    @Test
    void move_west_toANewPositionInsideTheWorld(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 1, 1); // Start at position (1,1)
        bot.turnTo(West);

        bot.move();

        assertEquals(new Coord(0, 1), bot.position()); // Should move west to (0,1)
    }

    @Test
    void move_east_toANewPositionInsideTheWorld(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 1, 1); // Start at position (1,1)
        bot.turnTo(East);

        bot.move();

        assertEquals(new Coord(2, 1), bot.position()); // Should move east to (2,1)
    }

    @Test
    void move_north_toANewPositionOutsideTheWorld(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 1, 0); // Start at north edge (1,0)
        bot.turnTo(North);

        bot.move(); // Try to move north beyond world boundary

        assertEquals(new Coord(1, 0), bot.position()); // Should stay at (1,0)
    }

    @Test
    void move_south_toANewPositionOutsideTheWorld(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 1, 2); // Start at south edge (1,2) - world is 3x3, so y=2 is bottom
        bot.turnTo(South);

        bot.move(); // Try to move south beyond world boundary

        assertEquals(new Coord(1, 2), bot.position()); // Should stay at (1,2)
    }

    @Test
    void move_west_toANewPositionOutsideTheWorld(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 0, 1); // Start at west edge (0,1)
        bot.turnTo(West);

        bot.move(); // Try to move west beyond world boundary

        assertEquals(new Coord(0, 1), bot.position()); // Should stay at (0,1)
    }

    @Test
    void move_east_toANewPositionOutsideTheWorld(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 2, 1); // Start at east edge (2,1) - world is 3x3, so x=2 is right edge
        bot.turnTo(East);

        bot.move(); // Try to move east beyond world boundary

        assertEquals(new Coord(2, 1), bot.position()); // Should stay at (2,1)
    }

    @Test
    void toString_includesPositionInformation(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 1, 2);

        String result = bot.toString();
        assertTrue(result.contains("ExplorerBot"));
        assertTrue(result.contains("1"));
        assertTrue(result.contains("2"));
    }

    @Test
    void newBot_hasDefaultFuelLevel100(){
        ExplorerBot bot = new ExplorerBot(world);
        assertEquals(100f, bot.fuelLevel(), 0.01f);
    }

    @Test
    void newBot_withCustomFuelLevel(){
        ExplorerBot bot = new ExplorerBot(world, 50f);
        assertEquals(50f, bot.fuelLevel(), 0.01f);
    }

    @Test
    void addFuel_increaseFuelLevel(){
        ExplorerBot bot = new ExplorerBot(world, 50f);
        bot.addFuel(25f);
        assertEquals(75f, bot.fuelLevel(), 0.01f);
    }

    @Test
    void addFuel_cannotExceed100Percent(){
        ExplorerBot bot = new ExplorerBot(world, 90f);
        bot.addFuel(20f);
        assertEquals(100f, bot.fuelLevel(), 0.01f);
    }

    @Test
    void fuelConsumptionPerKlik_defaultValue(){
        ExplorerBot bot = new ExplorerBot(world);
        assertEquals(1f, bot.fuelConsumptionPerKlik(), 0.01f);
    }

    @Test
    void setFuelConsumptionPerKlik_changesValue(){
        ExplorerBot bot = new ExplorerBot(world);
        bot.setFuelConsumptionPerKlik(2.5f);
        assertEquals(2.5f, bot.fuelConsumptionPerKlik(), 0.01f);
    }

    @Test
    void move_consumesFuel(){
        ExplorerBot bot = new ExplorerBot(world, 10f);
        world.add(bot, 1, 1);
        bot.setFuelConsumptionPerKlik(2f);

        bot.move();

        assertEquals(8f, bot.fuelLevel(), 0.01f);
    }

    @Test
    void move_withInsufficientFuel_doesNotMove(){
        ExplorerBot bot = new ExplorerBot(world, 0.5f);
        world.add(bot, 1, 1);
        bot.setFuelConsumptionPerKlik(1f);

        bot.move(); // Should not move due to insufficient fuel

        assertEquals(new Coord(1, 1), bot.position());
        assertEquals(0.5f, bot.fuelLevel(), 0.01f); // Fuel should not be consumed
    }

    @Test
    void move_withExactlyEnoughFuel_moves(){
        ExplorerBot bot = new ExplorerBot(world, 1f);
        world.add(bot, 1, 1);
        bot.setFuelConsumptionPerKlik(1f);
        bot.turnTo(North);

        bot.move();

        assertEquals(new Coord(1, 0), bot.position());
        assertEquals(0f, bot.fuelLevel(), 0.01f);
    }

    @Test
    void move_blockedByWorldBoundary_doesNotConsumeFuel(){
        ExplorerBot bot = new ExplorerBot(world, 10f);
        world.add(bot, 0, 0); // At north-west corner
        bot.turnTo(North);

        bot.move(); // Try to move north from edge

        assertEquals(new Coord(0, 0), bot.position());
        assertEquals(10f, bot.fuelLevel(), 0.01f); // Fuel should not be consumed
    }
}