package za.co.wethinkcode.botworld.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static za.co.wethinkcode.botworld.model.Heading.*;

public class ExplorerBotTest
{
    private World world = new World( 3, 3 );

    @Test
    void newBot_hasDefaultHeadingNorth(){
        ExplorerBot bot = new ExplorerBot(world);
        assertEquals(North, bot.heading());
    }

    @Test
    void turnTo_changesHeading(){
        ExplorerBot bot = new ExplorerBot(world);
        bot.turnTo(South);
        assertEquals(South, bot.heading());

        bot.turnTo(East);
        assertEquals(East, bot.heading());

        bot.turnTo(West);
        assertEquals(West, bot.heading());

        bot.turnTo(North);
        assertEquals(North, bot.heading());
    }

    @Test
    void move_north_toANewPositionInsideTheWorld(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 1, 1); // Start at position (1,1)
        bot.turnTo(North);

        bot.move();

        assertEquals(new Coord(1, 0), bot.position()); // Should move north to (1,0)
    }

    @Test
    void move_south_toANewPositionInsideTheWorld(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 1, 1); // Start at position (1,1)
        bot.turnTo(South);

        bot.move();

        assertEquals(new Coord(1, 2), bot.position()); // Should move south to (1,2)
    }

    @Test
    void move_west_toANewPositionInsideTheWorld(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 1, 1); // Start at position (1,1)
        bot.turnTo(West);

        bot.move();

        assertEquals(new Coord(0, 1), bot.position()); // Should move west to (0,1)
    }

    @Test
    void move_east_toANewPositionInsideTheWorld(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 1, 1); // Start at position (1,1)
        bot.turnTo(East);

        bot.move();

        assertEquals(new Coord(2, 1), bot.position()); // Should move east to (2,1)
    }

    @Test
    void move_north_toANewPositionOutsideTheWorld(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 1, 0); // Start at north edge (1,0)
        bot.turnTo(North);

        bot.move(); // Try to move north beyond world boundary

        assertEquals(new Coord(1, 0), bot.position()); // Should stay at (1,0)
    }

    @Test
    void move_south_toANewPositionOutsideTheWorld(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 1, 2); // Start at south edge (1,2) - world is 3x3, so y=2 is bottom
        bot.turnTo(South);

        bot.move(); // Try to move south beyond world boundary

        assertEquals(new Coord(1, 2), bot.position()); // Should stay at (1,2)
    }

    @Test
    void move_west_toANewPositionOutsideTheWorld(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 0, 1); // Start at west edge (0,1)
        bot.turnTo(West);

        bot.move(); // Try to move west beyond world boundary

        assertEquals(new Coord(0, 1), bot.position()); // Should stay at (0,1)
    }

    @Test
    void move_east_toANewPositionOutsideTheWorld(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 2, 1); // Start at east edge (2,1) - world is 3x3, so x=2 is right edge
        bot.turnTo(East);

        bot.move(); // Try to move east beyond world boundary

        assertEquals(new Coord(2, 1), bot.position()); // Should stay at (2,1)
    }

    @Test
    void toString_includesPositionInformation(){
        ExplorerBot bot = new ExplorerBot(world);
        world.add(bot, 1, 2);

        String result = bot.toString();
        assertTrue(result.contains("ExplorerBot"));
        assertTrue(result.contains("1"));
        assertTrue(result.contains("2"));
    }
}