package za.co.wethinkcode.botworld.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;
import static za.co.wethinkcode.botworld.model.Heading.*;

public class ExplorerBotTest
{
    private World world = new World( 3, 3 );

    @Test
    void move_north_toANewPositionInsideTheWorld(){
        fail( "TODO" );
    }

    @Test
    void move_south_toANewPositionInsideTheWorld(){
        fail( "TODO" );
    }

    @Test
    void move_west_toANewPositionInsideTheWorld(){
        fail( "TODO" );
    }

    @Test
    void move_east_toANewPositionInsideTheWorld(){
        fail( "TODO" );
    }

    @Test
    void move_north_toANewPositionOutsideTheWorld(){
        fail( "TODO" );
    }

    @Test
    void move_south_toANewPositionOutsideTheWorld(){
        fail( "TODO" );
    }

    @Test
    void move_west_toANewPositionOutsideTheWorld(){
        fail( "TODO" );
    }

    @Test
    void move_east_toANewPositionOutsideTheWorld(){
        fail( "TODO" );
    }
}