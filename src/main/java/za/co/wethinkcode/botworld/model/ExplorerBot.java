package za.co.wethinkcode.botworld.model;

import static com.google.common.base.Preconditions.checkNotNull;

public class ExplorerBot
{
    private World world;
    private Heading heading = Heading.North;
    private float fuelLevel = 100f;
    private float fuelPerKlik = 1f; // Default fuel consumption per klik

    public ExplorerBot( World aWorld ){
        this( aWorld, 100f );
    }

    public ExplorerBot( World aWorld, float startingFuelLevel ){
        world = checkNotNull( aWorld );
        fuelLevel = startingFuelLevel;
    }

    /**
     * Turn the receiver bot to a new heading.
     * @param newHeading What direction the receiver should face
     */
    public void turnTo( Heading newHeading ){
        heading = newHeading;
    }

    /**
     * Answer the receiver bot's current heading.
     * @return a Heading
     */
    public Heading heading(){
        return heading;
    }

    /**
     * Answer the receiver bot's current fuel level.
     * @return The current fuel level as a percentage (0f to 100f)
     */
    public float fuelLevel(){
        return fuelLevel;
    }

    /**
     * Add fuel to the receiver bot.
     * @param fuelToAdd The amount of fuel to add
     */
    public void addFuel( float fuelToAdd ){
        fuelLevel = Math.min( 100f, fuelLevel + fuelToAdd );
    }

    /**
     * Answer the receiver bot's fuel consumption per klik.
     * @return The fuel consumption per klik
     */
    public float fuelConsumptionPerKlik(){
        return fuelPerKlik;
    }

    /**
     * Set the receiver bot's fuel consumption per klik.
     * @param fuelPerKlik The fuel consumption per klik
     */
    public void setFuelConsumptionPerKlik( float fuelPerKlik ){
        this.fuelPerKlik = fuelPerKlik;
    }

    /**
     * Answer the receiver's current position in the world.
     * @return The receiver's current position.
     */
    public Coord position(){
        return world.locationOf( this );
    }

    /**
     * Move the receiver bot 1 klik in the direction it currently faces but not
     * beyond the edges of the world. If the move would result in the bot ending
     * up in an illegal position, simply ignore the request and don't move.
     * Movement consumes fuel - if there's insufficient fuel, the bot won't move.
     */
    public void move(){
        // Check if bot has enough fuel to move
        if( fuelLevel < fuelPerKlik ){
            return; // Not enough fuel to move
        }

        Coord newPosition = switch( heading() ){
            case North -> position().decrementY();
            case South -> position().incrementY();
            case West -> position().decrementX();
            case East -> position().incrementX();
        };

        if( world.contains( newPosition ) ){
            world.moveBot( this, newPosition );
            fuelLevel -= fuelPerKlik; // Consume fuel after successful move
        }
    }

    @Override
    public String toString(){
        return "[" + ExplorerBot.class.getSimpleName()
            + '@'
            + position()
            + ", fuel=" + String.format("%.1f", fuelLevel) + "%]";
    }
}
