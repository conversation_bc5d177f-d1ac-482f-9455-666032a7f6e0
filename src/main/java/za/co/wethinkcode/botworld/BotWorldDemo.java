package za.co.wethinkcode.botworld;

import za.co.wethinkcode.botworld.model.*;

/**
 * A simple demonstration of the ExplorerBot functionality.
 * This shows how bots can be created, added to a world, and moved around.
 */
public class BotWorldDemo {
    
    public static void main(String[] args) {
        System.out.println("Welcome to Robot World!");
        System.out.println("======================");
        
        // Create a small world
        World world = new World(5, 5);
        System.out.println("Created a 5x5 world");
        
        // Create an ExplorerBot
        ExplorerBot explorer = new ExplorerBot(world);
        System.out.println("Created an ExplorerBot");
        
        // Add the bot to the world at position (2, 2)
        world.add(explorer, 2, 2);
        System.out.println("Added bot to world at position: " + explorer.position());
        System.out.println("Bot is facing: " + explorer.heading());
        
        // Demonstrate movement in all directions
        System.out.println("\nDemonstrating movement:");
        System.out.println("======================");
        
        // Move North
        explorer.turnTo(Heading.North);
        System.out.println("Turned to face North");
        explorer.move();
        System.out.println("Moved North. New position: " + explorer.position());
        
        // Move East
        explorer.turnTo(Heading.East);
        System.out.println("Turned to face East");
        explorer.move();
        System.out.println("Moved East. New position: " + explorer.position());
        
        // Move South
        explorer.turnTo(Heading.South);
        System.out.println("Turned to face South");
        explorer.move();
        System.out.println("Moved South. New position: " + explorer.position());
        
        // Move West
        explorer.turnTo(Heading.West);
        System.out.println("Turned to face West");
        explorer.move();
        System.out.println("Moved West. New position: " + explorer.position());
        
        // Try to move outside world boundaries
        System.out.println("\nTesting world boundaries:");
        System.out.println("========================");
        
        // Move to edge and try to go beyond
        world.add(explorer, 0, 0); // Move to top-left corner
        System.out.println("Moved bot to top-left corner: " + explorer.position());
        
        explorer.turnTo(Heading.North);
        explorer.move(); // Try to move north from (0,0)
        System.out.println("Tried to move North from edge. Position: " + explorer.position());
        
        explorer.turnTo(Heading.West);
        explorer.move(); // Try to move west from (0,0)
        System.out.println("Tried to move West from edge. Position: " + explorer.position());
        
        System.out.println("\nBot details: " + explorer);
        System.out.println("Demo completed!");
    }
}
