package za.co.wethinkcode.botworld;

import za.co.wethinkcode.botworld.model.*;

/**
 * A simple demonstration of the ExplorerBot functionality.
 * This shows how bots can be created, added to a world, and moved around.
 */
public class BotWorldDemo {
    
    public static void main(String[] args) {
        System.out.println("Welcome to Robot World!");
        System.out.println("======================");
        
        // Create a small world
        World world = new World(5, 5);
        System.out.println("Created a 5x5 world");
        
        // Create an ExplorerBot with custom fuel settings
        ExplorerBot explorer = new ExplorerBot(world, 75f);
        explorer.setFuelConsumptionPerKlik(5f);
        System.out.println("Created an ExplorerBot with 75% fuel");

        // Add the bot to the world at position (2, 2)
        world.add(explorer, new Coord(2, 2));
        System.out.println("Added bot to world at position: " + explorer.position());
        System.out.println("Bot is facing: " + explorer.heading());
        System.out.println("Bot fuel level: " + explorer.fuelLevel() + "%");
        System.out.println("Fuel consumption per klik: " + explorer.fuelConsumptionPerKlik() + "%");
        
        // Demonstrate movement in all directions
        System.out.println("\nDemonstrating movement:");
        System.out.println("======================");
        
        // Move North
        explorer.turnTo(Heading.North);
        System.out.println("Turned to face North");
        explorer.move();
        System.out.println("Moved North. New position: " + explorer.position());
        System.out.println("Fuel after move: " + explorer.fuelLevel() + "%");

        // Move East
        explorer.turnTo(Heading.East);
        System.out.println("Turned to face East");
        explorer.move();
        System.out.println("Moved East. New position: " + explorer.position());
        System.out.println("Fuel after move: " + explorer.fuelLevel() + "%");

        // Move South
        explorer.turnTo(Heading.South);
        System.out.println("Turned to face South");
        explorer.move();
        System.out.println("Moved South. New position: " + explorer.position());
        System.out.println("Fuel after move: " + explorer.fuelLevel() + "%");

        // Move West
        explorer.turnTo(Heading.West);
        System.out.println("Turned to face West");
        explorer.move();
        System.out.println("Moved West. New position: " + explorer.position());
        System.out.println("Fuel after move: " + explorer.fuelLevel() + "%");
        
        // Try to move outside world boundaries
        System.out.println("\nTesting world boundaries:");
        System.out.println("========================");
        
        // Move to edge and try to go beyond
        world.add(explorer, new Coord(0, 0)); // Move to top-left corner
        System.out.println("Moved bot to top-left corner: " + explorer.position());
        
        explorer.turnTo(Heading.North);
        explorer.move(); // Try to move north from (0,0)
        System.out.println("Tried to move North from edge. Position: " + explorer.position());
        
        explorer.turnTo(Heading.West);
        explorer.move(); // Try to move west from (0,0)
        System.out.println("Tried to move West from edge. Position: " + explorer.position());

        // Demonstrate fuel management
        System.out.println("\nDemonstrating fuel management:");
        System.out.println("==============================");

        System.out.println("Current fuel: " + explorer.fuelLevel() + "%");
        explorer.addFuel(20f);
        System.out.println("Added 20% fuel. New level: " + explorer.fuelLevel() + "%");

        // Try to move until out of fuel
        System.out.println("\nMoving until out of fuel:");
        int moveCount = 0;
        while(explorer.fuelLevel() >= explorer.fuelConsumptionPerKlik()) {
            explorer.turnTo(Heading.East);
            explorer.move();
            moveCount++;
            System.out.println("Move " + moveCount + ": Position " + explorer.position() +
                             ", Fuel: " + String.format("%.1f", explorer.fuelLevel()) + "%");
        }

        System.out.println("\nTrying to move with insufficient fuel:");
        explorer.move();
        System.out.println("Position unchanged: " + explorer.position());
        System.out.println("Fuel unchanged: " + String.format("%.1f", explorer.fuelLevel()) + "%");

        System.out.println("\nBot details: " + explorer);
        System.out.println("Demo completed!");
    }
}
